from datetime import MAXYEAR
import datetime
from os import path
from pathlib import Path
import time
from xmlrpc.client import DateTime
# from cv2.typing import Range  # 注释掉这行，因为可能不存在
from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
import socket
import struct
import math
import yaml
import numpy as np # 用于处理大量的数值数据，NumPy 库提供了高效的数组运算方法
import matplotlib
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import cv2

matplotlib.use('TkAgg')  # 使用Tkinter作为后端
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from tkinter import Label, StringVar

# 1.通过激光雷达识别特定区域角度的点云
class Lidar:
    
    # 创建连接并读取配置文件中的配置项
    def __init__(self):
        # 1.1 用UDP方式将激光雷达点云数据实时读取到python工程中（读取数据：点云的弧度+距离+能量/反射强度）
        # 设置服务器地址和端口
        self.server_ip = '127.0.0.1'
        self.server_port = 3008
        # 创建UDP套接字
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # 绑定套接字到地址和端口
        self.sock.bind((self.server_ip, self.server_port))
        print(f"🚀 增强版雷达数据接收器启动")
        print(f"📡 UDP server listening on {self.server_ip}:{self.server_port}")
        print(f"📊 支持文本和二进制格式数据解析")
        
        self.cnt=0 # 保存数据的索引
        # 后续需要用到的数据初始化
        self.Pos_arr=[]
        self.PointCloud_all=[]
        self.completedNum=0# 记录获取点云的完成圈数
        
        self.x_coords=[]# 用于记录由真实点云换算出的笛卡尔坐标分离的self.X坐标，生成拟合直线需要提供的数据
        self.y_coords=[]# 用于记录由真实点云换算出的笛卡尔坐标分离的self.Y坐标，生成拟合直线需要提供的数据
        
        self.x_fittingLine=[]# 用于记录拟合直线上的点云的X坐标
        self.y_fittingLine=[]# 用于记录拟合直线上的点云的Y坐标
        self.theta_fittingLine=[]# 用于记录拟合直线上的点云的角度（弧度制，弧度是角度的一种度量方式）
        self.r_fittingLine=[]# 用于记录拟合直线上的点云的半径/距离（毫米制）
        self.fittingLine=[]# 包含拟合直线点云的笛卡尔坐标+极坐标+弧度
        self.k=0
        self.b=0

        self.theta = np.array([])# 夹角数组（弧度制，弧度是角度的一种度量方式）
        self.r = np.array([])# 半径/距离数组（毫米制）
        

        # 1.2将特定区域角度的数据阈值放到配置文件中（见.yaml文件），并读取到python工程中
        # 读取self.YAML文件
        with open('params.yaml', 'r') as file:
            self.config = yaml.safe_load(file)# 安全（不执行任意Python代码，防止执行恶意代码）得解析self.YAML格式的文件
        # 访问配置参数，获取深度值过滤区间
        self.startAngle = self.config['ScanPointCloudZone']['startAngle']# 最小角度
        self.endAngle = self.config['ScanPointCloudZone']['endAngle']# 最大角度
        self.startDistance = self.config['ScanPointCloudZone']['startDistance']# 最小深度值（距离）
        self.endDistance = self.config['ScanPointCloudZone']['endDistance']# 最大深度值（距离）
        self.CompensationAngle = self.config['ScanPointCloudZone']['CompensationAngle']# 角度补偿值
        self.pointCloudMapSwitch = self.config['pointCloudMapSwitch']# 点云图显示开关

    def parse_received_data(self, data):
        """解析接收到的雷达数据 - 支持文本和二进制格式"""
        try:
            # 首先尝试解析为文本格式
            text_result = self.parse_text_format(data)
            if text_result:
                return text_result

            # 如果文本解析失败，尝试二进制格式
            return self.parse_binary_format(data)

        except Exception as e:
            print(f"解析数据时出错: {e}")
            return None

    def parse_text_format(self, data):
        """解析文本格式的雷达数据"""
        try:
            # 尝试将数据解码为文本
            text_data = data.decode('utf-8', errors='ignore').strip()
            if not text_data:
                return None

            # 按空格分割数据：角度 距离 强度
            arr = text_data.split(' ')
            if len(arr) >= 3:
                try:
                    angle_rad = float(arr[0])  # 弧度
                    distance_m = float(arr[1])  # 米
                    confidence = float(arr[2])  # 强度

                    # 验证数据合理性
                    if (0 <= angle_rad <= 2*math.pi and
                        0 <= distance_m <= 100 and
                        0 <= confidence <= 255):

                        return {
                            'format': 'text',
                            'angle_rad': angle_rad,
                            'distance_m': distance_m,
                            'confidence': confidence,
                            'raw_text': text_data
                        }
                except ValueError:
                    return None

            return None

        except UnicodeDecodeError:
            return None

    def parse_binary_format(self, data):
        """解析二进制格式的UserData结构体"""
        try:
            if len(data) < 28:
                return None

            # 解析UserData基本字段
            offset = 0
            data_type = struct.unpack('I', data[offset:offset+4])[0]
            offset += 4

            idx = struct.unpack('i', data[offset:offset+4])[0]
            offset += 4

            connectArg1 = data[offset:offset+16].decode('utf-8', errors='ignore').rstrip('\x00')
            offset += 16

            connectArg2 = struct.unpack('i', data[offset:offset+4])[0]
            offset += 4

            # 如果是FRAMEDATA类型(1)，尝试提取点数据
            if data_type == 1 and len(data) > 100:
                # 尝试从数据中提取单个点的信息
                points = self.extract_points_from_binary(data, offset)
                if points:
                    # 返回第一个有效点
                    return {
                        'format': 'binary',
                        'type': data_type,
                        'idx': idx,
                        'connectArg1': connectArg1,
                        'connectArg2': connectArg2,
                        'angle_rad': points[0]['angle_rad'],
                        'distance_m': points[0]['distance_m'],
                        'confidence': points[0]['confidence']
                    }

            return None

        except Exception as e:
            print(f"解析二进制数据时出错: {e}")
            return None

    def extract_points_from_binary(self, data, start_offset):
        """从二进制数据中提取点云数据"""
        points = []
        try:
            # 尝试不同的偏移量来寻找点数据
            for test_offset in range(start_offset, min(start_offset + 200, len(data) - 100), 4):
                test_points = []
                current_offset = test_offset

                # 尝试解析前几个点
                for i in range(min(10, (len(data) - current_offset) // 9)):
                    if current_offset + 9 > len(data):
                        break

                    try:
                        angle = struct.unpack('f', data[current_offset:current_offset+4])[0]
                        distance = struct.unpack('f', data[current_offset+4:current_offset+8])[0]
                        confidence = struct.unpack('B', data[current_offset+8:current_offset+9])[0]

                        # 检查数值是否合理
                        if (0 <= angle <= 2*math.pi and 0 <= distance <= 100 and 0 <= confidence <= 255):
                            test_points.append({
                                'angle_rad': angle,
                                'distance_m': distance,
                                'confidence': confidence
                            })
                            current_offset += 9
                        else:
                            break
                    except:
                        break

                if len(test_points) >= 3:  # 如果找到足够多的点，认为解析成功
                    return test_points

            return []

        except Exception as e:
            print(f"提取点数据时出错: {e}")
            return []

    # 获取点云数据并生成拟合直线【获取3圈点云数据后，停止继续获取点云和生成拟合直线】
    def obtainPointCloudData(self):
        try:
            # 1.3根据自定义的特定区域角度数据阈值筛选点云数据
            while True:
                # 接收数据 - 使用增强的接收逻辑
                self.data, self.addr = self.sock.recvfrom(65536)  # 增大缓冲区以支持二进制数据

                # 解析接收到的数据
                parsed_data = self.parse_received_data(self.data)
                if not parsed_data:
                    # 调试信息：显示无法解析的数据
                    if len(self.data) < 100:  # 只显示小数据包的内容
                        print(f"⚠️  无法解析数据包: 长度={len(self.data)}, 内容={self.data[:50]}")
                    else:
                        print(f"⚠️  无法解析数据包: 长度={len(self.data)}, 前50字节={self.data[:50]}")
                    continue  # 如果解析失败，继续接收下一个数据包

                # 提取角度、距离、强度信息
                angle_rad = parsed_data['angle_rad']
                distance_m = parsed_data['distance_m']
                confidence = parsed_data['confidence']

                # 调试信息：显示成功解析的数据
                if hasattr(self, 'debug_count'):
                    self.debug_count += 1
                else:
                    self.debug_count = 1

                if self.debug_count % 100 == 1:  # 每100个点显示一次
                    print(f"✅ 成功解析点#{self.debug_count}: 格式={parsed_data['format']}, 角度={math.degrees(angle_rad):.1f}°, 距离={distance_m*1000:.0f}mm, 强度={confidence}")

                # 为了兼容原有逻辑，创建arr数组
                self.arr = [str(angle_rad), str(distance_m), str(confidence)]

                # 1.4将筛选出的点云数据保存到python内存中（self.PointCloud_result）【第一层筛选】
                
                self.startAngleJudge=False
                self.endAngleJudge=False
                self.AngleJudge=False
                
                self.flag1_s=False
                self.flag1_e=False
                self.flag1=False# s>0,e<0的标志
                self.flag2_s=False
                self.flag2_e=False
                self.flag2=False# s<0,e>0的标志
                self.startAngle = self.config['ScanPointCloudZone']['startAngle']# 最小角度
                self.endAngle = self.config['ScanPointCloudZone']['endAngle']# 最大角度
                # 将角度归一化到0-360°范围内
                self.startAngle = self.startAngle - self.CompensationAngle
                if self.startAngle > 0:
                    self.flag1_s=True
                else:
                    self.flag2_s=True
                    self.startAngle = np.mod(self.startAngle,360)
                self.endAngle = self.endAngle - self.CompensationAngle
                if self.endAngle > 0:
                     self.flag2_e=True
                else:
                    self.flag1_e=True
                    self.endAngle = np.mod(self.endAngle,360)
                if self.flag1_s and self.flag1_e:
                    self.flag1=True
                if self.flag2_s and self.flag2_e:
                    self.flag2=True
                # 分情况处理
                if self.flag1:
                    self.startAngleJudge = (math.degrees(float(self.arr[0]) - math.radians(self.CompensationAngle)) <= self.startAngle)
                    self.endAngleJudge = (math.degrees(float(self.arr[0]) - math.radians(self.CompensationAngle)) >= self.endAngle)
                    self.AngleJudge=self.startAngleJudge or self.endAngleJudge
                if self.flag2:
                    self.startAngleJudge = (math.degrees(float(self.arr[0]) - math.radians(self.CompensationAngle)) >= self.startAngle)
                    self.endAngleJudge = (math.degrees(float(self.arr[0]) - math.radians(self.CompensationAngle)) <= self.endAngle)
                    self.AngleJudge=self.startAngleJudge or self.endAngleJudge
                else:
                    self.startAngleJudge = (math.degrees(float(self.arr[0]) - math.radians(self.CompensationAngle)) >= self.startAngle)
                    self.endAngleJudge = (math.degrees(float(self.arr[0]) - math.radians(self.CompensationAngle)) <= self.endAngle)
                    self.AngleJudge=self.startAngleJudge and self.endAngleJudge

                if self.AngleJudge and round(float(self.arr[1])*1000) >= self.startDistance and round(float(self.arr[1])*1000) <= self.endDistance:# math.degrees函数用于将弧度值转换成角度值
                    # 2.计算纸箱的高度
                    # 2.1取点：代表纸箱下边缘的点（用拟合直线的方式）：
                    # 用极坐标（角度+距离）换算成self.X、self.Y坐标（二维点云数据）【单位：毫米】
                    self.X = float(self.arr[1]) * 1000 * math.cos(float(self.arr[0]) - math.radians(self.CompensationAngle)) # 这里运算对象必须是弧度
                    self.Y = float(self.arr[1]) * 1000 * math.sin(float(self.arr[0]) - math.radians(self.CompensationAngle))# 这里运算对象必须是弧度
                    # 存储每个传递进来的点云数据
                    self.PointCloud={
                        "angle":float(f"{math.degrees(float(self.arr[0])) - self.CompensationAngle:.2f}"),# 换算成角度，保留小数点后两位
                        "distance":round(float(self.arr[1])*1000),# 精确到单位毫米mm
                        "confidence":round(float(self.arr[2])),# 取整【因为能量值表示一个百分比，直接取整不影响精度】（可以用来大致判断物体材质：反光材料，则数字越大）
                    }
                    # 将点云数据和self.Xself.Y坐标做个对应关系，用于后续通过选取点的self.X查找到对应点的信息
                    self.PointCloud_all.append({
                        "X":self.X,
                        "Y":self.Y,# 精确到单位毫米mm
                        "radian":float(self.arr[0]) - math.radians(self.CompensationAngle),
                        "distance":round(float(self.arr[1])*1000),
                        "PointCloud":self.PointCloud
                    })
                    
                    # 更新数据数组（真实点云）
                    self.theta = np.append(self.theta, float(self.arr[0]) - math.radians(self.CompensationAngle))# 新增当前点的夹角（单位是弧度）
                    self.r = np.append(self.r, round(float(self.arr[1])*1000))# 新增当前点的距离（在图中以毫米为单位）

                    self.Pos=[self.X,self.Y]
                    # 将新的二维点云数据放进数组中
                    if self.Pos not in self.Pos_arr:
                        # 笛卡尔坐标数组（真实点云）
                        self.Pos_arr.append(self.Pos) 

                    # 当获取到的角度突然减小时，说明雷达激光束已扫描完一圈【扫描区肯定有没反射过来的光束（盲区），所以角度回传是填不满区间的，不用覆盖角度判断】
                    self.after_angle=None# 记录上一轮获取到的点云角度
                    self.current_angle=None# 记录本轮获取到的点云角度
                    if self.completedNum < 3:# 小于3圈才判断
                        if len(self.PointCloud_all) > 1:
                            self.after_angle=self.PointCloud_all[-2]["PointCloud"]["angle"]# 倒二元素
                            self.current_angle=self.PointCloud_all[-1]["PointCloud"]["angle"]# 最后一个元素
                            if self.current_angle < self.after_angle:
                                self.completedNum += 1
                                return 1# 本函数需要后续继续调用的回传标志，通过它退出本函数死循环(按圈为更新点云图的依据)【第一轮到第三轮都需要绘图】
                    elif self.completedNum == 3:
                        self.calcFittingLine()# 调用将真实点云处理成拟合直线的函数
                        return 2# 本函数不需要后续继续调用的回传标志，通过它退出本函数死循环
        except KeyboardInterrupt:
            print("Server closing...")
        
    # 计算并生成拟合直线（在点云数据获取完毕【扫描3圈】后将真实点云处理成拟合直线）
    def calcFittingLine(self):
        try:
            # 分离self.X坐标和self.Y坐标
            for pos in self.Pos_arr:
                self.x_coords.append(pos[0])
                self.y_coords.append(pos[1])

            # 输出点云的下端点的信息
            self.LowerEndpoint_y_coords = sorted(self.y_coords)# 倒序排序
            for point in self.PointCloud_all:
                if self.LowerEndpoint_y_coords[0] == point["Y"]:
                    self.LowerEndpoint_X=point["X"]
                    self.LowerEndpoint_distance=point["distance"]
            #print(f"LowerEndpoint:  X:{self.LowerEndpoint_X}  Y:{self.LowerEndpoint_y_coords[0]}   distance:{self.LowerEndpoint_distance}")
            
            # 对self.x_coords和self.y_coords的第二层过滤（为了排除干扰点云对拟合直线生成的影响）
            # self.record_allStrip_PointCloud_Pos=[]# 记录所有条点云的XY（判断点云条数的条件：本轮两点间距离大等于上一轮两点间距离近似为5倍，将当前点以及后续点加入“一条点云线”的记录中，直到遇到下一个判定条件）
            # self.x_coords_temporary=[]# 临时存放当前条点云的x值
            # self.y_coords_temporary=[]# 临时存放当前条点云的y值
            # self.record_r_diffrence_after=None# 记录每一次比较的距离（上一个差值数值）
            # self.record_r_diffrence_current=None# 记录每一次比较的距离（本次差值数值）
            # self.record_for_num=0# 记录循环次数，当第二次进入循环时才能开始取两点间Y差值进行比较
            # # ①遍历降序排序后的self.y_coords，并计算点云中上下点云的Y差值
            # self.y_coords = sorted(self.y_coords, reverse=True)# 使用sorted()函数进行降序排序
            # for y in self.y_coords:
            #     for point in self.PointCloud_all:
            #         if point["Y"] == y:
            #             self.record_for_num += 1
            #             if self.record_for_num == 1:# 第一次循环
            #                 self.x_coords_temporary.append(point["X"])# 临时存放当前条点云的x值
            #                 self.y_coords_temporary.append(y)# 临时存放当前条点云的y值
            #             elif self.record_for_num == 2:# 第二次循环，直接赋初值给差值变量：self.record_r_diffrence_after
            #                 self.record_r_diffrence_after=math.sqrt(math.pow(point["X"],2)+math.pow(y,2))
            #             elif self.record_for_num > 2:# 从第三次循环开始，才能进行三点间两段距离的数值倍数比较
            #                 self.record_r_diffrence_current=math.sqrt(math.pow(point["X"],2)+math.pow(y,2))# 本轮距离
            #                 # 比较本轮距离和上一轮距离的倍数
            #                 if round(self.record_r_diffrence_current*10 / self.record_r_diffrence_after*10) >= 2:# 本轮两点间距离大等于上一轮两点间距离近似为2倍
            #                     self.record_allStrip_PointCloud_Pos.append({
            #                         "x_coords_temporary":self.x_coords_temporary,
            #                         "y_coords_temporary":self.y_coords_temporary
            #                     })
            #                     self.x_coords_temporary=[]# 当前条点云的X存放结束，清空数组
            #                     self.y_coords_temporary=[]# 当前条点云的Y存放结束，清空数组
            #                     self.x_coords_temporary.append(point["X"])# 将当前点记录到下一条点云笛卡尔坐标中
            #                     self.y_coords_temporary.append(y)# 将当前点记录到下一条点云笛卡尔坐标中
            #                 else:
            #                     self.x_coords_temporary.append(point["X"])# 临时存放当前条点云的x值
            #                     self.y_coords_temporary.append(y)# 临时存放当前条点云的y值

            # # ②情况3：有1条连续点云
            # if len(self.record_allStrip_PointCloud_Pos) == 1:                    
            #     self.x_coords=self.record_allStrip_PointCloud_Pos[0]["x_coords_temporary"]
            #     self.y_coords=self.record_allStrip_PointCloud_Pos[0]["y_coords_temporary"]     

            # # ③情况1：有2条连续点云
            # # 取下方那条点云为纸箱点云进行拟合直线的计算        
            # # ④情况2：有3条连续点云
            # # 取中间那条点云为纸箱点云进行拟合直线的计算
            # elif len(self.record_allStrip_PointCloud_Pos) > 1:       
            #     self.x_coords=self.record_allStrip_PointCloud_Pos[1]["x_coords_temporary"]
            #     self.y_coords=self.record_allStrip_PointCloud_Pos[1]["y_coords_temporary"]
            

            # # 计算拟合直线上的点
            # self.k,self.b=np.polyfit(self.x_coords,self.y_coords,1)# 使用polyfit来拟合一条直线（一次多项式）【原理：最小二乘法】
            # #最小二乘法的手动计算：
            # x_mean = np.mean([self.x_coords])# x均值
            # y_mean = np.mean([self.y_coords])# y均值        
            # numerator = np.sum((self.x_coords - x_mean) * (self.y_coords - y_mean))# x和y的协方差
            # denominator = np.sum((self.x_coords - x_mean)**2)# x的方差
            # self.k = numerator / denominator
            # self.b = y_mean - self.k * x_mean
            
            # opencv的霍夫直线
            # 创建一个空白图像
            image_width = 4000
            image_height = 4000
            image = np.zeros((image_height, image_width) + (1,), dtype=np.uint8)  # 单通道灰度图
            # 将点云的XY变换成以左下角为原点的数据
            width_center=image_width/2
            height_center=image_height/2
            # 转换格式
            self.xy_coords=[]
            for num in range(0,len(self.x_coords)-1):
                self.xy_coords.append([round(self.x_coords[num] + width_center), round(self.y_coords[num] + height_center)])
            self.xy_coords=np.array(self.xy_coords)
            
            # 在图像上绘制点云
            for xy in self.xy_coords:
                x, y = xy# xy要求是整数，并控制距离在一定范围内
                if 0 <= x <= image_width and 0 <= y <=image_height:
                    image[y, x] = 255  # 设置点为白色
                    a=1
            # 应用概率霍夫线变换：用函数cv2.HoughLinesP可得xy
            lines = cv2.HoughLinesP(image, rho=1, theta=np.pi/180, threshold=5, minLineLength=10, maxLineGap=100)# rho原点到直线的距离，theta直线与X轴的夹角，threshold直线段投票数的阈值（线段上的最小点数），minLineLength最小线段长度，maxLineGap线段上最大允许的间隔

            self.min_x=None
            self.min_y=None
            self.max_x=None
            self.max_y=None
            # 检查是否检测到了直线段：对霍夫直线上的点进行检测
            if lines is not None:
                for line in lines:# line是二维数组
                    x1 = line[0][0]
                    y1 = line[0][1]
                    x2 = line[0][2]
                    y2 = line[0][3]
                    # 取最小Y，并记录笛卡尔坐标：为了在图像上绘制直线
                    # 取最大Y，并记录笛卡尔坐标：为了在图像上绘制直线
                    if self.min_x is None and self.min_y is None and self.max_x is None and self.max_y is None:# 第一次循环的比较
                        if y1 < y2:
                            self.min_x=x1
                            self.min_y=y1
                            self.max_x=x2
                            self.max_y=y2
                    else:# 后续循环的比较
                        if y1 < y2:
                            if y1 < self.min_y:
                                self.min_x=x1
                                self.min_y=y1
                            elif y2 > self.max_y:
                                self.max_x=x2
                                self.max_y=y2
                        elif y1 > y2:
                            if y1 > self.max_y:
                                self.max_x=x1
                                self.max_y=y1
                            elif y2 < self.min_y:
                                self.min_x=x2
                                self.min_y=y2
             
            # 还原坐标
            self.x_fittingLine = [round(self.min_x - width_center), round(self.max_x - width_center)]
            self.y_fittingLine=[round(self.min_y - height_center), round(self.max_y - height_center)]
            self.theta_fittingLine=[]
            self.r_fittingLine=[]
            for num in range(0,len(self.x_fittingLine)):# range()区间：左开右闭。实际循环2次
                theta=np.atan2(self.y_fittingLine[num],self.x_fittingLine[num])
                r=np.sqrt(np.pow(self.x_fittingLine[num],2)+np.pow(self.y_fittingLine[num],2))
                self.theta_fittingLine.append(theta)# atan2会自动处理象限问题:这个函数已经考虑了 y 和 x 的符号，因此能够正确地返回所有四个象限的角度。计算结果是弧度，而不是角度。
                self.r_fittingLine.append(r)
        except KeyboardInterrupt:
            print("Server closing...")
        

    
    # 计算纸箱高度（纸箱下边缘到雷达的距离）
    def calcHeight(self):
        try:
            self.boxBottomEdge_num=None# 代表纸箱下边缘的数值在数组中的所在索引
            if self.y_fittingLine[0] < self.y_fittingLine[1]:
                self.boxBottomEdge_num=0
            else:
                self.boxBottomEdge_num=1
            # 记录纸箱下边缘的信息
            self.boxBottomEdge_x=self.x_fittingLine[self.boxBottomEdge_num]# 记录纸箱下边缘的X值
            self.boxBottomEdge_y=self.y_fittingLine[self.boxBottomEdge_num]# 记录纸箱下边缘的Y值（直线最小Y值）
            self.boxBottomEdge_distance=self.r_fittingLine[self.boxBottomEdge_num]# 记录点半径（距离）
            # 计算高度
            self.lidar_to_carton_height=abs(self.boxBottomEdge_distance * math.sin(self.theta_fittingLine[self.boxBottomEdge_num]))
            
            # 显示在窗体
            # self.boxBottomEdge_y = str(self.boxBottomEdge_y)
            if self.lidar_to_carton_height != 0 and self.lidar_to_carton_height != None:
                return 1
            else:
                return 0
        except KeyboardInterrupt:
            print("Server closing...")

    # 绘制雷达点云图（绘制方法：将Matplotlib图形嵌入到Tkinter窗口中）
    def drawPointCloudMap(self, root):
        self.root = root
        self.root.title("实时点云图")
        # 创建窗口
        self.fig = plt.figure()# 创建图形窗口
        self.ax = self.fig.add_subplot(111, polar=True)# 在图形窗口中创建极坐标子图,这是一个三位数的子图网格参数。它表示在1行1列的网格中创建第1个子图
        
        self.ax.set_ylim(0, 1600)# 设置极坐标子图的半径范围（绘制的点云距离的范围）
        
        self.scatter_real = self.ax.scatter(self.theta, self.r, s=0.3, c='red', marker='o')# 初始化散点图（真实点云）
        self.scatter_fittingLine, = self.ax.plot(self.theta_fittingLine, self.r_fittingLine, c='blue')  # 初始化拟合直线

        self.canvas = FigureCanvasTkAgg(self.fig, master=root)
        
        # 添加导航工具栏到Tkinter窗口
        self.toolbar = NavigationToolbar2Tk(self.canvas, self.root)
        self.toolbar.update()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)
        
        # 连接鼠标移动事件到更新函数
        self.canvas.mpl_connect('motion_notify_event', self.update_mouse_position)

        # 初始化鼠标坐标文本
        self.mouse_coords_text = self.ax.text(0.05, 0.05, '', transform=self.ax.transAxes,fontsize=12, color='black', bbox=dict(boxstyle='round', fc='white'))

        flag = self.obtainPointCloudData()
        if flag == 1:
            self.root.after(1, self.update_data_and_plot)  # 设置定时器更新数据和图形
            
    # 更新点云极坐标
    def update_data_and_plot(self):
        st=datetime.datetime.now()
        print(f"now:{st}")
        # 更新散点图（真实点云）
        self.scatter_real.set_offsets(np.c_[self.theta, self.r])
        # 将Matplotlib图形嵌入到Tkinter窗口中
        self.canvas.draw()  # 重新绘制图形
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)
        # 再次设置定时器更新数据和图形
        flag = self.obtainPointCloudData()
        if flag == 1:# 继续调用获取点云数据和生成拟合直线的函数
            self.timer_id = self.root.after(1, self.update_data_and_plot)# 递归
        elif flag == 2:# 点云数据获取完毕（扫描完3圈），要调用计算高度的函数
            # 更新拟合直线
            self.scatter_fittingLine.set_data(np.array(self.theta_fittingLine), np.array(self.r_fittingLine)) # 绘制拟合直线
            self.root.after_cancel(self.timer_id)
            self.timer_id = None
            # 调用计算高度函数
            ret = self.calcHeight()
            if ret == 1:
                print(f"boxBottomEdge_x:{self.boxBottomEdge_x}   boxBottomEdge_y:{self.boxBottomEdge_y}   boxBottomEdge_distance:{self.boxBottomEdge_distance}     lidar_to_carton_height:{self.lidar_to_carton_height}")
            else:
                print("calc carton height error")
                
            
            # 获取当前日期和时间
            now = datetime.datetime.now()
            # 提取年、月和日
            current_year = now.year
            current_month = now.month
            current_day = now.day
            # 提取时、分、秒
            current_hour = now.hour
            current_minute = now.minute
            current_second = now.second
            # 保留样本
            self.cnt += 1    
            pic_path=f'D:\视觉样本\{current_year}年-{current_month}月-{current_day}日\图片样本'
            pic_name=f'D:\视觉样本\{current_year}年-{current_month}月-{current_day}日\图片样本\{current_hour}_{current_minute}_{current_second}[{self.cnt}].png'
            data_name=f'D:\视觉样本\{current_year}年-{current_month}月-{current_day}日\数据样本.txt'
            if not Path(pic_path).exists():
                Path(pic_path).mkdir(parents=True)
            plt.savefig(pic_name)
            if not Path(data_name).exists():
                with open(data_name, 'w') as file:# 创建文件并写入
                    file.write(f"boxBottomEdge_x:{self.boxBottomEdge_x}   boxBottomEdge_y:{self.boxBottomEdge_y}   boxBottomEdge_distance:{self.boxBottomEdge_distance}     lidar_to_carton_height:{self.lidar_to_carton_height}\n")
                    file.write('——\n')
            else:
                with open(data_name, 'a') as file:# 追加写入
                    file.write(f"boxBottomEdge_x:{self.boxBottomEdge_x}   boxBottomEdge_y:{self.boxBottomEdge_y}   boxBottomEdge_distance:{self.boxBottomEdge_distance}     lidar_to_carton_height:{self.lidar_to_carton_height}\n")
                    file.write('——\n')
            self.root.destroy()# 直接关闭窗口并终止Tkinter程序
            while True:# 死循环重新绘制并计算数据
                self.clear_data()
                self.calc_carton_height()
                
    def update_mouse_position(self, event):
        if event.inaxes:
            # 获取鼠标在极坐标中的位置（角度和半径）
            theta = event.xdata
            r = event.ydata
            # 将极坐标转换为笛卡尔坐标
            x = r * np.cos(theta)
            y = r * np.sin(theta)
            # 更新鼠标坐标文本
            self.mouse_coords_text.set_text(f'X: {x:.2f}\nY: {y:.2f}')
            self.canvas.draw()  # 重新绘制图形以更新文本
        else:
            self.mouse_coords_text.set_text('')
            self.canvas.draw()  # 重新绘制图形以清除文本
            
    # 清空上一轮数据的函数（用作当前轮的计算）
    def clear_data(self):
        self.completedNum = 0
        # 数据初始化
        self.Pos_arr=[]
        self.PointCloud_all=[]
        self.completedNum=0# 记录获取点云的完成圈数
        
        self.x_coords=[]# 用于记录由真实点云换算出的笛卡尔坐标分离的self.X坐标，生成拟合直线需要提供的数据
        self.y_coords=[]# 用于记录由真实点云换算出的笛卡尔坐标分离的self.Y坐标，生成拟合直线需要提供的数据
        
        self.x_fittingLine=[]# 用于记录拟合直线上的点云的X坐标
        self.y_fittingLine=[]# 用于记录拟合直线上的点云的Y坐标
        self.theta_fittingLine=[]# 用于记录拟合直线上的点云的角度（弧度制，弧度是角度的一种度量方式）
        self.r_fittingLine=[]# 用于记录拟合直线上的点云的半径/距离（毫米制）
        self.fittingLine=[]# 包含拟合直线点云的笛卡尔坐标+极坐标+弧度
        self.k=0
        self.b=0

        self.theta = np.array([])# 夹角数组（弧度制，弧度是角度的一种度量方式）
        self.r = np.array([])# 半径/距离数组（毫米制）
        
        # 1.2将特定区域角度的数据阈值放到配置文件中（见.yaml文件），并读取到python工程中【重新读取配置项的值】
        # 读取self.YAML文件
        with open('params.yaml', 'r') as file:
            self.config = yaml.safe_load(file)# 安全（不执行任意Python代码，防止执行恶意代码）得解析self.YAML格式的文件
        # 访问配置参数，获取深度值过滤区间
        self.startAngle = self.config['ScanPointCloudZone']['startAngle']# 最小角度
        self.endAngle = self.config['ScanPointCloudZone']['endAngle']# 最大角度
        self.startDistance = self.config['ScanPointCloudZone']['startDistance']# 最小深度值（距离）
        self.endDistance = self.config['ScanPointCloudZone']['endDistance']# 最大深度值（距离）
        self.CompensationAngle = self.config['ScanPointCloudZone']['CompensationAngle']# 角度补偿值
        self.pointCloudMapSwitch = self.config['pointCloudMapSwitch']# 点云图显示开关 

    # 供外部使用的封装函数：纸箱测高（纸箱下边缘到雷达的高度）
    def calc_carton_height(self):
        if self.pointCloudMapSwitch == 'True':
            root = tk.Tk()
            ret = LDSE200A.drawPointCloudMap(root)
            root.mainloop()# 会阻塞程序执行，mainloop()实际上是在进入一个事件监听循环，它处理所有的窗口事件（如按钮点击、关闭窗口等），不可直接放在处理点云数据的死循环中
        else:
            while True:
                ret = self.obtainPointCloudData()
                if ret == 1:
                    continue
                elif ret == 2:
                    ret_calc = self.calcHeight()
                    if ret_calc == 1:
                        return True, self.boxBottomEdge_x, self.boxBottomEdge_y, self.boxBottomEdge_distance, self.lidar_to_carton_height
                    else:
                        return False,None
                else:
                    return False,None
    

    

if __name__ == '__main__':
    LDSE200A=Lidar()# 实例化雷达
    while True:
        if LDSE200A.pointCloudMapSwitch == 'True':
            LDSE200A.calc_carton_height()
        else:
            #st=datetime.datetime.now()
            '''
            boxBottomEdge_x：纸箱下边缘的X值
            boxBottomEdge_y：纸箱下边缘的Y值（直线最小Y值）
            boxBottomEdge_distance：点半径（纸箱下边缘的点到雷达的距离）
            lidar_to_carton_height：纸箱下边缘到雷达的高度
            '''
            ret, boxBottomEdge_x, boxBottomEdge_y, boxBottomEdge_distance, lidar_to_carton_height = LDSE200A.calc_carton_height()
            #en=datetime.datetime.now()
            #time=en-st
            #print(f"time:{time}")
            print(f"ret:{ret}   boxBottomEdge_x:{boxBottomEdge_x}   boxBottomEdge_y:{boxBottomEdge_y}   boxBottomEdge_distance:{boxBottomEdge_distance}     lidar_to_carton_height:{lidar_to_carton_height}")
        LDSE200A.clear_data()

    