#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版Lidar.py的数据接收功能
"""

import sys
import time
from Lidar import Lidar

def test_lidar_data_reception():
    """测试雷达数据接收功能"""
    print("🧪 测试增强版Lidar数据接收功能")
    print("=" * 60)
    
    try:
        # 创建Lidar实例
        lidar = Lidar()
        
        print("📡 开始接收雷达数据...")
        print("⏱️  将运行30秒进行测试，然后自动停止")
        print("🔍 观察是否能成功解析数据...")
        print("-" * 60)
        
        # 测试数据接收30秒
        start_time = time.time()
        test_duration = 30  # 秒
        
        received_count = 0
        
        while time.time() - start_time < test_duration:
            try:
                # 接收一个数据包
                data, addr = lidar.sock.recvfrom(65536)
                received_count += 1
                
                # 尝试解析数据
                parsed_data = lidar.parse_received_data(data)
                
                if parsed_data:
                    print(f"✅ 包#{received_count}: {parsed_data['format']}格式, "
                          f"角度={parsed_data['angle_rad']:.3f}rad, "
                          f"距离={parsed_data['distance_m']:.3f}m, "
                          f"强度={parsed_data['confidence']}")
                else:
                    if received_count % 10 == 1:  # 每10个失败包显示一次
                        print(f"❌ 包#{received_count}: 解析失败, 长度={len(data)}")
                
                # 每100个包显示一次统计
                if received_count % 100 == 0:
                    elapsed = time.time() - start_time
                    print(f"📊 统计: 已接收{received_count}个包, 用时{elapsed:.1f}秒")
                
            except Exception as e:
                print(f"❌ 接收数据时出错: {e}")
                break
        
        print("\n" + "=" * 60)
        print(f"🏁 测试完成!")
        print(f"📈 总计接收: {received_count} 个数据包")
        print(f"⏱️  测试时长: {test_duration} 秒")
        
        if received_count > 0:
            print("✅ 数据接收功能正常")
        else:
            print("❌ 未接收到任何数据，请检查:")
            print("   1. C++雷达程序是否正在运行")
            print("   2. 是否发送到正确的端口(3008)")
            print("   3. 防火墙设置")
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断测试")
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
    finally:
        print("🔌 关闭连接")

def test_lidar_full_functionality():
    """测试完整的Lidar功能"""
    print("\n🧪 测试完整Lidar功能")
    print("=" * 60)
    
    try:
        # 创建Lidar实例
        lidar = Lidar()
        
        print("🎯 开始测试obtainPointCloudData方法...")
        print("⚠️  这将运行完整的点云数据获取流程")
        print("📊 需要获取3圈数据才会停止")
        print("-" * 60)
        
        # 调用原有的点云数据获取方法
        result = lidar.obtainPointCloudData()
        
        if result == 1:
            print("✅ 成功完成一圈数据获取")
        elif result == 2:
            print("✅ 成功完成3圈数据获取，开始计算拟合直线")
        else:
            print(f"⚠️  获取数据返回值: {result}")
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断测试")
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")

def main():
    print("🔧 选择测试模式:")
    print("1. 快速数据接收测试 (30秒)")
    print("2. 完整功能测试 (获取3圈数据)")
    print("3. 退出")
    
    try:
        choice = input("\n请选择测试模式 (1/2/3): ").strip()
        
        if choice == '1':
            test_lidar_data_reception()
        elif choice == '2':
            test_lidar_full_functionality()
        elif choice == '3':
            print("👋 退出测试")
            return
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 程序退出")
    except Exception as e:
        print(f"❌ 程序出错: {e}")

if __name__ == "__main__":
    main()
