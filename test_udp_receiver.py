#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UDP数据接收测试脚本
用于测试从C++程序接收雷达数据
"""

import socket
import struct
import time
import math

class LidarUDPReceiver:
    def __init__(self, server_ip='127.0.0.1', server_port=3008):
        self.server_ip = server_ip
        self.server_port = server_port
        self.socket = None

    def parse_userdata(self, data):
        """解析UserData结构体"""
        try:
            if len(data) < 28:
                return None

            # 解析UserData基本字段
            # struct UserData {
            #     DataType type;        // 4 bytes (int)
            #     int idx;              // 4 bytes
            #     char connectArg1[16]; // 16 bytes
            #     int connectArg2;      // 4 bytes
            #     SpanData spandata;    // 很大的结构体
            #     FrameData framedata;  // 包含vector，实际传输时需要特殊处理
            # }

            offset = 0
            data_type = struct.unpack('I', data[offset:offset+4])[0]
            offset += 4

            idx = struct.unpack('i', data[offset:offset+4])[0]
            offset += 4

            connectArg1 = data[offset:offset+16].decode('utf-8', errors='ignore').rstrip('\x00')
            offset += 16

            connectArg2 = struct.unpack('i', data[offset:offset+4])[0]
            offset += 4

            result = {
                'type': data_type,
                'idx': idx,
                'connectArg1': connectArg1,
                'connectArg2': connectArg2,
                'raw_data_length': len(data)
            }

            # 如果是FRAMEDATA类型(1)，尝试解析更多数据
            if data_type == 1 and len(data) > 100:
                # 跳过SpanData部分，寻找FrameData
                # FrameData开始位置大约在offset + SpanData大小之后
                # SpanData包含一个完整的RawData结构，大小很大

                # 尝试在数据中寻找可能的点云数据
                points = self.extract_point_data(data, offset)
                if points:
                    result['points'] = points
                    result['point_count'] = len(points)

            return result

        except Exception as e:
            print(f"解析UserData时出错: {e}")
            return None

    def extract_point_data(self, data, start_offset):
        """尝试从数据中提取点云数据"""
        points = []
        try:
            # 在数据中寻找可能的DataPoint结构
            # struct DataPoint { float angle; float distance; unsigned char confidence; }
            # 每个点大小: 4 + 4 + 1 = 9字节，但由于内存对齐可能是12字节

            # 寻找合理的浮点数值范围来识别点数据
            offset = start_offset
            point_size = 9  # 假设没有内存对齐

            # 尝试不同的偏移量来找到点数据的开始位置
            for test_offset in range(start_offset, min(start_offset + 200, len(data) - 100), 4):
                test_points = []
                current_offset = test_offset

                # 尝试解析前几个点
                for i in range(min(10, (len(data) - current_offset) // point_size)):
                    if current_offset + 9 > len(data):
                        break

                    try:
                        angle = struct.unpack('f', data[current_offset:current_offset+4])[0]
                        distance = struct.unpack('f', data[current_offset+4:current_offset+8])[0]
                        confidence = struct.unpack('B', data[current_offset+8:current_offset+9])[0]

                        # 检查数值是否合理
                        if (0 <= angle <= 2*math.pi and 0 <= distance <= 100 and 0 <= confidence <= 255):
                            test_points.append({
                                'angle_rad': angle,
                                'angle_deg': angle * 180 / math.pi,
                                'distance_m': distance,
                                'distance_mm': distance * 1000,
                                'confidence': confidence
                            })
                            current_offset += point_size
                        else:
                            break
                    except:
                        break

                # 如果找到了合理的点数据，继续解析更多
                if len(test_points) >= 3:
                    # 继续解析更多点
                    while current_offset + 9 <= len(data) and len(test_points) < 2000:
                        try:
                            angle = struct.unpack('f', data[current_offset:current_offset+4])[0]
                            distance = struct.unpack('f', data[current_offset+4:current_offset+8])[0]
                            confidence = struct.unpack('B', data[current_offset+8:current_offset+9])[0]

                            if (0 <= angle <= 2*math.pi and 0 <= distance <= 100 and 0 <= confidence <= 255):
                                test_points.append({
                                    'angle_rad': angle,
                                    'angle_deg': angle * 180 / math.pi,
                                    'distance_m': distance,
                                    'distance_mm': distance * 1000,
                                    'confidence': confidence
                                })
                                current_offset += point_size
                            else:
                                break
                        except:
                            break

                    if len(test_points) > 10:  # 如果找到足够多的点，认为解析成功
                        return test_points

            return []

        except Exception as e:
            print(f"提取点数据时出错: {e}")
            return []

    def start_receiving(self):
        """开始接收UDP数据"""
        try:
            # 创建UDP socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.bind((self.server_ip, self.server_port))

            print(f"UDP接收器启动，监听 {self.server_ip}:{self.server_port}")
            print("等待雷达数据...")
            print("=" * 60)

            packet_count = 0

            while True:
                try:
                    # 接收数据
                    data, addr = self.socket.recvfrom(65536)  # 最大64KB
                    packet_count += 1
                    
                    print(f"\n=== 数据包 #{packet_count} ===")
                    print(f"来源地址: {addr}")
                    print(f"数据长度: {len(data)} 字节")

                    # 解析UserData结构
                    parsed_data = self.parse_userdata(data)
                    if parsed_data:
                        print(f"数据类型: {parsed_data['type']} ({'FRAMEDATA' if parsed_data['type'] == 1 else 'SPANDATA' if parsed_data['type'] == 0 else 'UNKNOWN'})")
                        print(f"帧索引: {parsed_data['idx']}")
                        print(f"连接参数: {parsed_data['connectArg1']} @ {parsed_data['connectArg2']}")

                        # 如果解析到了点云数据
                        if 'points' in parsed_data and parsed_data['points']:
                            points = parsed_data['points']
                            print(f"解析到 {len(points)} 个数据点")

                            # 统计有效点
                            valid_points = [p for p in points if p['distance_m'] > 0.001]
                            if valid_points:
                                distances = [p['distance_mm'] for p in valid_points]
                                min_dist = min(distances)
                                max_dist = max(distances)
                                avg_dist = sum(distances) / len(distances)

                                print(f"有效点数: {len(valid_points)}")
                                print(f"距离范围: {min_dist:.1f}mm - {max_dist:.1f}mm")
                                print(f"平均距离: {avg_dist:.1f}mm")

                                # 显示前10个有效点的详细信息
                                print("\n前10个有效数据点:")
                                shown = 0
                                for i, point in enumerate(points):
                                    if point['distance_m'] > 0.001 and shown < 10:
                                        print(f"  点{i:3d}: 角度={point['angle_deg']:6.2f}° 距离={point['distance_mm']:7.1f}mm 强度={point['confidence']:3d}")
                                        shown += 1
                            else:
                                print("未找到有效的距离数据")
                        else:
                            print("未能解析到点云数据")
                            # 如果标准解析失败，尝试简单分析
                            if len(data) > 100:
                                self.simple_data_analysis(data)
                    else:
                        print("数据解析失败")
                        # 如果解析失败，尝试简单分析
                        if len(data) > 100:
                            self.simple_data_analysis(data)

                    # 显示原始数据的前32字节（十六进制）
                    hex_data = ' '.join([f'{b:02x}' for b in data[:32]])
                    print(f"原始数据(前32字节): {hex_data}")

                    # 每10个包显示一次统计
                    if packet_count % 10 == 0:
                        print(f"\n{'='*20} 已接收 {packet_count} 个数据包 {'='*20}")

                except socket.timeout:
                    print("接收超时，继续等待...")
                    continue
                except KeyboardInterrupt:
                    print("\n用户中断，停止接收")
                    break
                except Exception as e:
                    print(f"接收数据时出错: {e}")
                    continue
                    
        except Exception as e:
            print(f"启动UDP接收器失败: {e}")
        finally:
            if self.socket:
                self.socket.close()
                print("UDP socket已关闭")

    def simple_data_analysis(self, data):
        """简单的数据分析，寻找可能的距离和角度数据"""
        try:
            print("\n=== 简单数据分析 ===")

            # 寻找浮点数模式的数据
            float_values = []
            for i in range(0, len(data) - 4, 4):
                try:
                    val = struct.unpack('f', data[i:i+4])[0]
                    if 0 < val < 100:  # 可能的距离值(米)
                        float_values.append((i, val, 'distance'))
                    elif 0 <= val <= 2*math.pi:  # 可能的角度值(弧度)
                        float_values.append((i, val, 'angle'))
                except:
                    continue

            if float_values:
                print(f"找到 {len(float_values)} 个可能的浮点数值:")
                for i, (offset, val, data_type) in enumerate(float_values[:20]):  # 只显示前20个
                    if data_type == 'distance':
                        print(f"  偏移{offset:4d}: {val:.3f}m ({val*1000:.1f}mm) - 可能的距离")
                    else:
                        print(f"  偏移{offset:4d}: {val:.3f}rad ({val*180/math.pi:.1f}°) - 可能的角度")
            else:
                print("未找到合理的浮点数值")

        except Exception as e:
            print(f"简单数据分析出错: {e}")

def main():
    print("雷达UDP数据接收器")
    print("=" * 60)
    print("此程序将接收并解析从C++雷达程序发送的UDP数据")
    print("按Ctrl+C停止接收")
    print("=" * 60)

    receiver = LidarUDPReceiver()
    receiver.start_receiving()

if __name__ == "__main__":
    main()
