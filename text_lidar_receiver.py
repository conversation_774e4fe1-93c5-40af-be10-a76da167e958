#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本格式雷达数据接收器
专门用于接收和解析文本格式的雷达数据（基于Lidar.py的逻辑）
"""

import socket
import math
import time

class TextLidarReceiver:
    def __init__(self, server_ip='127.0.0.1', server_port=3008):
        self.server_ip = server_ip
        self.server_port = server_port
        self.socket = None
        self.point_count = 0
        self.angle_compensation = 0  # 角度补偿值，可以根据需要调整
        
    def start_receiving(self):
        """开始接收文本格式的雷达数据"""
        try:
            # 创建UDP socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.bind((self.server_ip, self.server_port))
            
            print("🚀 文本格式雷达数据接收器启动")
            print(f"📡 监听地址: {self.server_ip}:{self.server_port}")
            print("📊 数据格式: 角度(弧度) 距离(米) 强度")
            print("=" * 60)
            
            while True:
                try:
                    # 接收数据
                    data, addr = self.socket.recvfrom(4096)
                    self.point_count += 1
                    
                    # 解析文本数据
                    parsed_point = self.parse_text_data(data)
                    if parsed_point:
                        self.display_point_data(parsed_point)
                    else:
                        print(f"❌ 数据包 #{self.point_count}: 解析失败")
                        print(f"   原始数据: {data}")
                        
                except socket.timeout:
                    print("⏰ 接收超时，继续等待...")
                    continue
                except KeyboardInterrupt:
                    print("\n🛑 用户中断，停止接收")
                    break
                except Exception as e:
                    print(f"❌ 接收数据时出错: {e}")
                    continue
                    
        except Exception as e:
            print(f"❌ 启动失败: {e}")
        finally:
            if self.socket:
                self.socket.close()
                print("🔌 连接已关闭")
    
    def parse_text_data(self, data):
        """解析文本格式的雷达数据"""
        try:
            # 解码为文本
            text_data = data.decode('utf-8', errors='ignore').strip()
            if not text_data:
                return None
            
            # 按空格分割：角度 距离 强度
            arr = text_data.split(' ')
            if len(arr) >= 3:
                try:
                    angle_rad = float(arr[0])  # 弧度
                    distance_m = float(arr[1])  # 米
                    confidence = float(arr[2])  # 强度
                    
                    # 验证数据合理性
                    if (0 <= angle_rad <= 2*math.pi and 
                        0 <= distance_m <= 100 and 
                        0 <= confidence <= 255):
                        
                        # 应用角度补偿（基于Lidar.py的逻辑）
                        compensated_angle_rad = angle_rad - math.radians(self.angle_compensation)
                        
                        # 转换单位
                        angle_deg = math.degrees(compensated_angle_rad)
                        distance_mm = round(distance_m * 1000)
                        
                        # 计算笛卡尔坐标
                        x_mm = distance_m * 1000 * math.cos(compensated_angle_rad)
                        y_mm = distance_m * 1000 * math.sin(compensated_angle_rad)
                        
                        return {
                            'original_angle_rad': angle_rad,
                            'original_angle_deg': math.degrees(angle_rad),
                            'compensated_angle_rad': compensated_angle_rad,
                            'compensated_angle_deg': angle_deg,
                            'distance_m': distance_m,
                            'distance_mm': distance_mm,
                            'confidence': confidence,
                            'x_mm': x_mm,
                            'y_mm': y_mm,
                            'raw_text': text_data
                        }
                except ValueError:
                    return None
            
            return None
            
        except UnicodeDecodeError:
            return None
    
    def display_point_data(self, point):
        """显示点数据"""
        # 每100个点显示一次详细信息
        if self.point_count % 100 == 1:
            print(f"\n📍 点 #{self.point_count}")
            print(f"   原始角度: {point['original_angle_deg']:.2f}° ({point['original_angle_rad']:.4f}rad)")
            print(f"   补偿角度: {point['compensated_angle_deg']:.2f}° ({point['compensated_angle_rad']:.4f}rad)")
            print(f"   距离: {point['distance_mm']:.0f}mm ({point['distance_m']:.3f}m)")
            print(f"   强度: {point['confidence']:.0f}")
            print(f"   坐标: X={point['x_mm']:.1f}mm, Y={point['y_mm']:.1f}mm")
            print(f"   原始: {point['raw_text']}")
        
        # 每10个点显示一次简要信息
        elif self.point_count % 10 == 1:
            print(f"📍 点#{self.point_count}: {point['compensated_angle_deg']:.1f}° {point['distance_mm']:.0f}mm 强度{point['confidence']:.0f}")
        
        # 每1000个点显示一次统计
        if self.point_count % 1000 == 0:
            print("─" * 60)
            print(f"✅ 已接收 {self.point_count} 个数据点")
            print("─" * 60)

class AdvancedTextLidarReceiver(TextLidarReceiver):
    """高级文本雷达接收器，包含数据统计和分析功能"""
    
    def __init__(self, server_ip='127.0.0.1', server_port=3008):
        super().__init__(server_ip, server_port)
        self.points_buffer = []
        self.buffer_size = 100  # 缓存最近100个点
        
    def display_point_data(self, point):
        """显示点数据并进行统计分析"""
        # 添加到缓存
        self.points_buffer.append(point)
        if len(self.points_buffer) > self.buffer_size:
            self.points_buffer.pop(0)
        
        # 每50个点显示一次统计
        if self.point_count % 50 == 0:
            self.display_statistics()
        
        # 显示当前点
        if self.point_count % 10 == 1:
            print(f"📍 点#{self.point_count}: {point['compensated_angle_deg']:.1f}° {point['distance_mm']:.0f}mm 强度{point['confidence']:.0f}")
    
    def display_statistics(self):
        """显示统计信息"""
        if not self.points_buffer:
            return
        
        distances = [p['distance_mm'] for p in self.points_buffer]
        angles = [p['compensated_angle_deg'] for p in self.points_buffer]
        confidences = [p['confidence'] for p in self.points_buffer]
        
        print(f"\n📊 统计信息 (最近{len(self.points_buffer)}个点):")
        print(f"   距离: {min(distances):.0f}mm - {max(distances):.0f}mm (平均: {sum(distances)/len(distances):.0f}mm)")
        print(f"   角度: {min(angles):.1f}° - {max(angles):.1f}°")
        print(f"   强度: {min(confidences):.0f} - {max(confidences):.0f} (平均: {sum(confidences)/len(confidences):.0f})")
        print("─" * 40)

def main():
    print("🔧 选择接收器模式:")
    print("1. 基础模式 - 显示基本点数据")
    print("2. 高级模式 - 包含统计分析")
    
    try:
        choice = input("请选择 (1/2): ").strip()
        if choice == '2':
            receiver = AdvancedTextLidarReceiver()
            print("🚀 启动高级文本雷达接收器...")
        else:
            receiver = TextLidarReceiver()
            print("🚀 启动基础文本雷达接收器...")
        
        receiver.start_receiving()
        
    except KeyboardInterrupt:
        print("\n👋 程序退出")

if __name__ == "__main__":
    main()
