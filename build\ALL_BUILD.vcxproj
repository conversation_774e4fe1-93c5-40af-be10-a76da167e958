﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{027F2507-7280-3E7A-8DD3-0107BCFBAD57}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\workfile\lanhai-driver-branch_noweb\sdk;D:\workfile\lanhai-driver-branch_noweb\sdk\service;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\workfile\lanhai-driver-branch_noweb\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/workfile/lanhai-driver-branch_noweb/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\python3.10\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/workfile/lanhai-driver-branch_noweb -BD:/workfile/lanhai-driver-branch_noweb/build --check-stamp-file D:/workfile/lanhai-driver-branch_noweb/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCCompiler.cmake.in;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCCompilerABI.c;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCInformation.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXCompiler.cmake.in;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXCompilerABI.cpp;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCompilerIdDetection.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCXXCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompilerABI.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompilerId.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompilerSupport.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineRCCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineSystem.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeFindBinUtils.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeParseLibraryArchitecture.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCCompiler.cmake.in;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystem.cmake.in;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestCCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestCXXCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestCompilerCommon.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestRCCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CrayClang-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\OrangeC-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\TI-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\TIClang-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Tasking-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CompilerId\VS-10.vcxproj.in;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeDetermineLinkerId.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeInspectCLinker.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeInspectCXXLinker.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\FeatureTesting.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Determine-CXX.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\python3.10\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\workfile\lanhai-driver-branch_noweb\build\CMakeFiles\4.0.3\CMakeCCompiler.cmake;D:\workfile\lanhai-driver-branch_noweb\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;D:\workfile\lanhai-driver-branch_noweb\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;D:\workfile\lanhai-driver-branch_noweb\build\CMakeFiles\4.0.3\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\workfile\lanhai-driver-branch_noweb\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\workfile\lanhai-driver-branch_noweb\build\ZERO_CHECK.vcxproj">
      <Project>{2BF05C38-77EC-3BA9-B417-6CF4E4A6139B}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\workfile\lanhai-driver-branch_noweb\build\demo.vcxproj">
      <Project>{540116FE-F92C-3FF7-80ED-F922B0A39447}</Project>
      <Name>demo</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>