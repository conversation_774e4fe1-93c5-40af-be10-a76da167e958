#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的雷达数据查看器
专门用于快速查看从C++程序接收的雷达数据
"""

import socket
import struct
import time
import math

class SimpleLidarViewer:
    def __init__(self, server_ip='127.0.0.1', server_port=3008):
        self.server_ip = server_ip
        self.server_port = server_port
        self.socket = None
        
    def start_viewing(self):
        """开始接收并显示雷达数据"""
        try:
            # 创建UDP socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.bind((self.server_ip, self.server_port))
            
            print("=" * 80)
            print(f"简化雷达数据查看器启动")
            print(f"监听地址: {self.server_ip}:{self.server_port}")
            print("等待雷达数据... (按Ctrl+C停止)")
            print("=" * 80)
            
            packet_count = 0
            
            while True:
                try:
                    # 接收数据
                    data, addr = self.socket.recvfrom(65536)
                    packet_count += 1
                    
                    print(f"\n📡 数据包 #{packet_count} | 长度: {len(data)} 字节 | 来源: {addr}")
                    
                    # 解析基本信息
                    if len(data) >= 28:
                        try:
                            data_type = struct.unpack('I', data[0:4])[0]
                            idx = struct.unpack('i', data[4:8])[0]
                            connectArg1 = data[8:24].decode('utf-8', errors='ignore').rstrip('\x00')
                            connectArg2 = struct.unpack('i', data[24:28])[0]
                            
                            type_name = "FRAMEDATA" if data_type == 1 else "SPANDATA" if data_type == 0 else f"UNKNOWN({data_type})"
                            print(f"📊 类型: {type_name} | 帧号: {idx} | 设备: {connectArg1}@{connectArg2}")
                            
                            # 如果是帧数据，尝试分析距离数据
                            if data_type == 1:
                                self.analyze_frame_data(data)
                                
                        except Exception as e:
                            print(f"❌ 基本信息解析失败: {e}")
                    
                    # 显示原始数据样本
                    hex_sample = ' '.join([f'{b:02x}' for b in data[:16]])
                    print(f"🔍 数据样本: {hex_sample}...")
                    
                    # 每10个包显示一次分隔线
                    if packet_count % 10 == 0:
                        print("─" * 80)
                        print(f"✅ 已成功接收 {packet_count} 个数据包")
                        print("─" * 80)
                        
                except socket.timeout:
                    print("⏰ 接收超时，继续等待...")
                    continue
                except KeyboardInterrupt:
                    print("\n\n🛑 用户中断，停止接收")
                    break
                except Exception as e:
                    print(f"❌ 接收数据时出错: {e}")
                    continue
                    
        except Exception as e:
            print(f"❌ 启动失败: {e}")
        finally:
            if self.socket:
                self.socket.close()
                print("🔌 连接已关闭")
    
    def analyze_frame_data(self, data):
        """分析帧数据中的距离信息"""
        try:
            # 寻找可能的距离数据（浮点数，范围0-100米）
            distances = []
            angles = []
            
            # 从偏移量50开始搜索，跳过头部信息
            for i in range(50, len(data) - 4, 4):
                try:
                    val = struct.unpack('f', data[i:i+4])[0]
                    if 0.01 < val < 50:  # 可能的距离值(米)
                        distances.append(val * 1000)  # 转换为毫米
                    elif 0 <= val <= 2*math.pi:  # 可能的角度值(弧度)
                        angles.append(val * 180 / math.pi)  # 转换为度
                except:
                    continue
            
            if distances:
                distances = distances[:100]  # 限制数量避免输出过多
                min_dist = min(distances)
                max_dist = max(distances)
                avg_dist = sum(distances) / len(distances)
                
                print(f"📏 发现 {len(distances)} 个可能的距离值:")
                print(f"   范围: {min_dist:.1f}mm - {max_dist:.1f}mm")
                print(f"   平均: {avg_dist:.1f}mm")
                
                # 显示前10个距离值
                sample_distances = distances[:10]
                dist_str = ", ".join([f"{d:.1f}mm" for d in sample_distances])
                print(f"   样本: {dist_str}")
            
            if angles:
                angles = angles[:50]  # 限制数量
                print(f"📐 发现 {len(angles)} 个可能的角度值:")
                sample_angles = angles[:10]
                angle_str = ", ".join([f"{a:.1f}°" for a in sample_angles])
                print(f"   样本: {angle_str}")
                
        except Exception as e:
            print(f"❌ 帧数据分析失败: {e}")

def main():
    print("🚀 启动简化雷达数据查看器...")
    viewer = SimpleLidarViewer()
    viewer.start_viewing()

if __name__ == "__main__":
    main()
