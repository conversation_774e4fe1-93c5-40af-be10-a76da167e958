﻿  Building Custom Rule D:/workfile/lanhai-driver-branch_noweb/CMakeLists.txt
  main.cpp
D:\workfile\lanhai-driver-branch_noweb\sdk\data.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../example/main.cpp”)
  
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.h(16,1): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
  (编译源文件“../example/main.cpp”)
  
D:\workfile\lanhai-driver-branch_noweb\example\main.cpp(44,32): warning C4244: “=”: 从“SOCKET”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\example\main.cpp(94,11): warning C4477: “printf”: 格式字符串“%d”需要类型“int”的参数，但可变参数 4 拥有了类型“unsigned __int64”
      D:\workfile\lanhai-driver-branch_noweb\example\main.cpp(94,11):
      请考虑在格式字符串中使用“%zd”
  
D:\workfile\lanhai-driver-branch_noweb\example\main.cpp(117,26): error C2589: “(”:“::”右边的非法标记
D:\workfile\lanhai-driver-branch_noweb\example\main.cpp(117,26): error C2059: 语法错误:“)”
D:\workfile\lanhai-driver-branch_noweb\example\main.cpp(139,31): error C2589: “(”:“::”右边的非法标记
D:\workfile\lanhai-driver-branch_noweb\example\main.cpp(139,31): error C2059: 语法错误:“)”
  Global.cpp
D:\workfile\lanhai-driver-branch_noweb\sdk\data.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../sdk/Global.cpp”)
  
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.h(16,1): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
  (编译源文件“../sdk/Global.cpp”)
  
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(141,50): warning C4244: “=”: 从“float”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(352,31): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(354,69): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(386,31): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(387,73): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(420,31): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(421,73): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(461,31): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(462,52): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(503,45): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(504,67): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(779,34): warning C4244: “=”: 从“int”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(780,63): warning C4244: “/=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(783,61): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(787,63): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(911,11): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(935,12): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(946,12): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(1003,14): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(1027,45): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(1029,45): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(1214,19): warning C4244: “=”: 从“time_t”转换到“long”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(1388,13): warning C4244: “初始化”: 从“SOCKET”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(1412,15): warning C4312: “类型强制转换”: 从“int”转换到更大的“HANDLE”
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(1491,22): warning C4312: “类型强制转换”: 从“int”转换到更大的“HANDLE”
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(1499,12): warning C4312: “类型强制转换”: 从“int”转换到更大的“HANDLE”
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(1579,9): warning C4311: “类型强制转换”: 从“HANDLE”到“int”的指针截断
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(1579,9): warning C4302: “类型强制转换”: 从“HANDLE”到“int”截断
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(2182,47): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(2183,66): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(2214,47): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(2215,66): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(2231,11): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(2240,73): warning C4244: “参数”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(2240,25): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(2240,19): warning C4244: “初始化”: 从“double”转换到“const float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(2266,12): warning C4477: “printf”: 格式字符串“%ld”需要类型“long”的参数，但可变参数 1 拥有了类型“unsigned __int64”
      D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp(2266,12):
      请考虑在格式字符串中使用“%zd”
  
  LidarDataProcess.cpp
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\workfile\lanhai-driver-branch_noweb\sdk\data.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../sdk/LidarDataProcess.cpp”)
  
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.h(16,1): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
  (编译源文件“../sdk/LidarDataProcess.cpp”)
  
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(95,91): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(109,90): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(227,41): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(272,43): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(286,44): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(311,64): warning C4244: “参数”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(318,45): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(610,64): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(735,51): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(749,51): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(797,40): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(801,39): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(846,41): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(890,44): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(904,45): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(920,65): warning C4244: “参数”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(927,46): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(1161,42): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(1163,42): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp(1165,53): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
  LidarCheckService.cpp
D:\workfile\lanhai-driver-branch_noweb\sdk\data.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../sdk/service/LidarCheckService.cpp”)
  
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.h(16,1): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
  (编译源文件“../sdk/service/LidarCheckService.cpp”)
  
D:\workfile\lanhai-driver-branch_noweb\sdk\service\LidarCheckService.cpp(24,6): warning C4311: “类型强制转换”: 从“HANDLE”到“int”的指针截断
D:\workfile\lanhai-driver-branch_noweb\sdk\service\LidarCheckService.cpp(24,6): warning C4302: “类型强制转换”: 从“HANDLE”到“int”截断
D:\workfile\lanhai-driver-branch_noweb\sdk\service\LidarCheckService.cpp(99,11): warning C4244: “初始化”: 从“SOCKET”转换到“int”，可能丢失数据
  standard_interface.cpp
D:\workfile\lanhai-driver-branch_noweb\sdk\data.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../sdk/standard_interface.cpp”)
  
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.h(16,1): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
  (编译源文件“../sdk/standard_interface.cpp”)
  
D:\workfile\lanhai-driver-branch_noweb\sdk\standard_interface.cpp(105,7): warning C4311: “类型强制转换”: 从“HANDLE”到“int”的指针截断
D:\workfile\lanhai-driver-branch_noweb\sdk\standard_interface.cpp(105,7): warning C4302: “类型强制转换”: 从“HANDLE”到“int”截断
D:\workfile\lanhai-driver-branch_noweb\sdk\standard_interface.cpp(123,7): warning C4311: “类型强制转换”: 从“HANDLE”到“int”的指针截断
D:\workfile\lanhai-driver-branch_noweb\sdk\standard_interface.cpp(123,7): warning C4302: “类型强制转换”: 从“HANDLE”到“int”截断
D:\workfile\lanhai-driver-branch_noweb\sdk\standard_interface.cpp(303,34): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\standard_interface.cpp(331,26): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\standard_interface.cpp(359,33): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\standard_interface.cpp(382,34): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\standard_interface.cpp(401,33): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\standard_interface.cpp(419,33): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\standard_interface.cpp(437,33): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\standard_interface.cpp(458,33): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\sdk\standard_interface.cpp(476,33): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
  正在生成代码...
