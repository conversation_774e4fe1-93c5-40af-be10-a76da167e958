﻿  main.cpp
D:\workfile\lanhai-driver-branch_noweb\sdk\data.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../example/main.cpp”)
  
D:\workfile\lanhai-driver-branch_noweb\sdk\Global.h(16,1): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
  (编译源文件“../example/main.cpp”)
  
D:\workfile\lanhai-driver-branch_noweb\example\main.cpp(45,32): warning C4244: “=”: 从“SOCKET”转换到“int”，可能丢失数据
D:\workfile\lanhai-driver-branch_noweb\example\main.cpp(95,11): warning C4477: “printf”: 格式字符串“%d”需要类型“int”的参数，但可变参数 4 拥有了类型“unsigned __int64”
      D:\workfile\lanhai-driver-branch_noweb\example\main.cpp(95,11):
      请考虑在格式字符串中使用“%zd”
  
  demo.vcxproj -> D:\workfile\lanhai-driver-branch_noweb\build\Debug\demo.exe
